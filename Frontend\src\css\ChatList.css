.chat-list {
  flex: 1;
  overflow-y: auto;
  background: #ffffff;
}

.no-chats {
  padding: 60px 20px;
  text-align: center;
  color: #667781;
}

.no-chats p {
  margin: 8px 0;
  font-size: 0.95rem;
  line-height: 1.4;
}

.no-chats p:first-child {
  font-weight: 500;
  color: #3b4a54;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e9edef;
  cursor: pointer;
  transition: background-color 0.15s ease;
  position: relative;
}

.chat-item:hover {
  background: #f0f2f5;
}

.chat-item.selected {
  background: #e7f3ff;
  border-right: 3px solid #00a884;
}

.chat-item.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #00a884;
}

.chat-avatar {
  width: 49px;
  height: 49px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  position: relative;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(11, 20, 26, 0.13);
}

.chat-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #00a884 0%, #128c7e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  font-size: 1.2rem;
  letter-spacing: 0.5px;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 13px;
  height: 13px;
  background: #25d366;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(11, 20, 26, 0.13);
}

.chat-info {
  flex: 1;
  min-width: 0;
  padding-right: 8px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 2px;
}

.chat-header-2 {
  display: flex;
  color: #f0f2f5;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 2px;
}

.chat-name {
  font-size: 1.0625rem;
  font-weight: 400;
  color: #111b21;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  max-width: calc(100% - 60px);
}

.chat-time {
  font-size: 0.75rem;
  color: #667781;
  flex-shrink: 0;
  margin-left: 8px;
  font-weight: 400;
  line-height: 1.2;
}

.last-message {
  font-size: 0.875rem;
  color: #667781;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  font-weight: 400;
  margin-top: 2px;
}

/* Scrollbar styling */
.chat-list::-webkit-scrollbar {
  width: 6px;
}

.chat-list::-webkit-scrollbar-track {
  background: transparent;
}

.chat-list::-webkit-scrollbar-thumb {
  background: rgba(11, 20, 26, 0.2);
  border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover {
  background: rgba(11, 20, 26, 0.3);
}

/* Additional professional styling */
.chat-item:last-child {
  border-bottom: none;
}

.chat-item:active {
  background: #d1f4cc;
}

/* Unread message indicator */
.chat-item .unread-count {
  background: #00a884;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 8px;
  padding: 0 6px;
}

/* Message status indicators */
.last-message.unread {
  color: #111b21;
  font-weight: 500;
}

/* Group chat indicator */
.chat-name.group {
  color: #111b21;
}

.chat-name.group::before {
  content: '👥';
  margin-right: 6px;
  font-size: 0.9rem;
}
