.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f2f5 0%, #e8f5e8 100%);
  padding: 20px;
  position: relative;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: #25d366;
  z-index: 0;
}

.auth-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 48px 40px;
  width: 100%;
  max-width: 420px;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-header h1 {
  color: #25d366;
  font-size: 2.8rem;
  margin: 0 0 8px 0;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.auth-header p {
  color: #667781;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 400;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}

.form-group input {
  padding: 18px 16px;
  border: 2px solid #e9edef;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
  color: #3b4a54;
}

.form-group input:focus {
  outline: none;
  border-color: #25d366;
  background: white;
  box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.form-group input::placeholder {
  color: #8696a0;
  font-weight: 400;
}

.auth-button {
  background: #25d366;
  color: white;
  border: none;
  padding: 18px 24px;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
  position: relative;
  overflow: hidden;
}

.auth-button:hover:not(:disabled) {
  background: #20b358;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.auth-button:active:not(:disabled) {
  transform: translateY(0);
}

.auth-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #fecaca;
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.auth-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f2f5;
}

.auth-footer p {
  color: #667781;
  margin: 0;
  font-size: 1rem;
}

.link-button {
  background: none;
  border: none;
  color: #25d366;
  cursor: pointer;
  font-weight: 600;
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.link-button:hover {
  color: #20b358;
  background: rgba(37, 211, 102, 0.1);
}

/* Add WhatsApp logo styling */
.auth-header::before {
  content: '💬';
  font-size: 3rem;
  display: block;
  margin-bottom: 16px;
}

/* Responsive design */
@media (max-width: 480px) {
  .auth-container {
    padding: 16px;
  }

  .auth-card {
    padding: 32px 24px;
    margin: 0 8px;
  }

  .auth-header h1 {
    font-size: 2.4rem;
  }
}
