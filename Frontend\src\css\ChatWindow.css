.chat-window {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #e5ddd5;
}

.chat-window-header {
  background: #25d366;
  color: white;
  padding: 15px 20px;
  border-bottom: 1px solid #128c7e;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-info h2 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.status {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.add-members-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-members-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.system-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0;
}

.system-message-content {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 0.85rem;
  text-align: center;
  max-width: 80%;
}

.system-message-time {
  font-size: 0.7rem;
  color: #999;
  margin-top: 4px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="chat-bg" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23chat-bg)"/></svg>');
}

.message {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.message.sent {
  justify-content: flex-end;
  margin-left: 60px;
}

.message.received {
  justify-content: flex-start;
  margin-right: 60px;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  margin-bottom: 2px;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
}

.avatar-spacer {
  width: 32px;
  height: 32px;
}

.message-content {
  max-width: 100%;
  padding: 8px 12px;
  border-radius: 12px;
  position: relative;
  word-wrap: break-word;
  min-width: 60px;
}

.message.sent .message-content {
  background: #dcf8c6;
  color: #333;
  border-bottom-right-radius: 4px;
}

.message.received .message-content {
  background: white;
  color: #333;
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sender-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #25d366;
  margin-bottom: 2px;
}

.message-text {
  font-size: 0.9rem;
  line-height: 1.3;
  margin-bottom: 4px;
}

.message-time {
  font-size: 0.7rem;
  color: #667781;
  text-align: right;
  margin-top: 2px;
}

.typing-indicator {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 15px;
}

.typing-content {
  background: white;
  padding: 10px 15px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.typing-dots {
  display: flex;
  gap: 3px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: 0.85rem;
  color: #666;
}

.message-input-form {
  background: white;
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
}

.message-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 8px 15px;
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 1rem;
  padding: 8px 0;
}

.send-button {
  background: #25d366;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  transition: background-color 0.3s ease;
}

.send-button:hover:not(:disabled) {
  background: #20b358;
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
