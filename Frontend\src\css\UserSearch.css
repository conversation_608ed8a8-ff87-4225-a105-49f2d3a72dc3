.user-search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.user-search-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.user-search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #25d366;
  color: white;
}

.back-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-search-header h2 {
  margin: 0;
  color: white;
  font-size: 1.3rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;
  padding: 5px;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.search-input-container {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.search-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: #25d366;
}

.users-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.loading {
  padding: 40px;
  text-align: center;
  color: #666;
}

.no-users {
  padding: 40px;
  text-align: center;
  color: #666;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.user-item:hover {
  background: #f8f9fa;
}

.user-item:last-child {
  border-bottom: none;
}

.user-item .user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  position: relative;
  flex-shrink: 0;
}

.user-item .user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-item .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.3rem;
}

.user-item .online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #25d366;
  border: 2px solid white;
  border-radius: 50%;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-info h3 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selected-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #e3f2fd;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  color: #25d366;
}

.next-button {
  background: #25d366;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s ease;
}

.next-button:hover {
  background: #20b358;
}

.user-item.selected {
  background: #e8f5e8;
  border-left: 3px solid #25d366;
}

.selection-indicator {
  color: #25d366;
  font-weight: bold;
  font-size: 1.2rem;
}

.group-form {
  padding: 20px;
}

.group-picture-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.group-picture-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 12px;
  border: 2px solid #e0e0e0;
}

.group-picture-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.group-picture-placeholder {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #999;
}

.change-picture-btn {
  background: #25d366;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

.group-name-section {
  margin-bottom: 20px;
}

.group-name-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
}

.group-name-input:focus {
  border-color: #25d366;
}

.selected-users {
  margin-bottom: 20px;
}

.selected-users h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
}

.selected-users-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-user-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f0f0f0;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
}

.selected-user-item .user-avatar {
  width: 24px;
  height: 24px;
}

.selected-user-item .avatar-placeholder {
  font-size: 0.8rem;
}

.group-actions {
  display: flex;
  justify-content: center;
}

.create-group-btn {
  background: #25d366;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

.create-group-btn:hover:not(:disabled) {
  background: #20b358;
}

.create-group-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Scrollbar styling */
.users-list::-webkit-scrollbar {
  width: 6px;
}

.users-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.users-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.users-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
