.chat-container {
  display: flex;
  height: 100vh;
  background: #f0f0f0;
}

.chat-sidebar {
  width: 350px;
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

/* Header styles moved to ChatHeader.css */

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.no-chat-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.welcome-message {
  text-align: center;
  color: #666;
}

.welcome-message h2 {
  margin: 0 0 10px 0;
  color: #25d366;
}

.welcome-message p {
  margin: 0;
  font-size: 1.1rem;
}

.chat-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #25d366;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }
  
  .chat-sidebar {
    width: 100%;
    height: 50vh;
  }
  
  .chat-main {
    height: 50vh;
  }
}
